{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "lint": "run-linter -d ./components -d ./pages -d ./server -d ./utils -e .ts -e .vue -f ./app.vue -f ./nuxt.config.ts -g -k -c 'pnpx vue-tsc --noEmit'", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@primeuix/themes": "^1.1.1", "@ti-platform/aide": "^3.7.0", "@ti-platform/aide-primevue": "^1.0.5", "@ti-platform/aide-vue": "^3.2.2", "@vuelidate/core": "^2.0.3", "@vueuse/core": "^13.3.0", "drizzle-orm": "^0.37.0", "drizzle-zod": "^0.5.1", "postgres": "^3.4.5", "nuxt": "^3.17.5", "primeicons": "^7.0.0", "primevue": "^4.3.5", "tailwindcss": "^4.1.8", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.16", "vue-router": "^4.5.1", "zod": "^3.25.67"}, "devDependencies": {"@primevue/nuxt-module": "^4.3.5", "@tailwindcss/vite": "^4.1.8", "@ti-platform/aide-build-tools": "^4.1.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^10.2.0", "prettier": "^3.5.3", "tsx": "^4.19.4", "drizzle-kit": "^0.30.0", "@types/pg": "^8.11.12"}}