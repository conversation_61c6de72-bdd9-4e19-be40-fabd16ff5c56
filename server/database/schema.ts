import { pgTable, serial, date, varchar, numeric, pgEnum } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

export const TABLE_NAME_STOCKS_MANAGER_EVENTS = 'stocks_manager_events';

// Define the event types
export const STOCKS_MANAGER_EVENT_TYPES = [
    'Dividend',
    'Purchase',
    'Reverse Split',
    'Sale'
] as const;

// Create the enum for PostgreSQL
export const stocksManagerEventTypeEnum = pgEnum('stocks_manager_event_type', STOCKS_MANAGER_EVENT_TYPES);

// Create the constant object for easy access
export const StocksManagerEventType = {
    DIVIDEND: 'Dividend',
    PURCHASE: 'Purchase',
    REVERSE_SPLIT: 'Reverse Split',
    SALE: 'Sale',
} as const satisfies Record<string, typeof STOCKS_MANAGER_EVENT_TYPES[number]>;

// Drizzle table definition
export const stocksManagerEventsTable = pgTable(TABLE_NAME_STOCKS_MANAGER_EVENTS, {
    id: serial('id').primaryKey(),
    date: date('date').notNull(),
    symbol: varchar('symbol', { length: 10 }).notNull(),
    type: stocksManagerEventTypeEnum('type').notNull(),
    quantity: numeric('quantity', { precision: 10, scale: 4 }).default('0').notNull(),
    price_per_share: numeric('price_per_share', { precision: 10, scale: 4 }).default('0').notNull(),
    fees: numeric('fees', { precision: 10, scale: 4 }).default('0').notNull(),
    total: numeric('total', { precision: 10, scale: 4 }).default('0').notNull(),
});

// Zod schemas for validation using drizzle-zod
export const insertStocksManagerEventSchema = createInsertSchema(stocksManagerEventsTable, {
    symbol: z.string().max(10).transform((s: string) => s.toUpperCase()),
    quantity: z.coerce.number().optional().default(0),
    price_per_share: z.coerce.number().optional().default(0),
    fees: z.coerce.number().optional().default(0),
    total: z.coerce.number().optional().default(0),
}).transform((data: any) => ({
    ...data,
    quantity: data.quantity?.toString() || '0',
    price_per_share: data.price_per_share?.toString() || '0',
    fees: data.fees?.toString() || '0',
    total: data.total?.toString() || '0',
}));

export const selectStocksManagerEventSchema = createSelectSchema(stocksManagerEventsTable);

export const updateStocksManagerEventSchema = createInsertSchema(stocksManagerEventsTable, {
    symbol: z.string().max(10).transform((s: string) => s.toUpperCase()).optional(),
    quantity: z.coerce.number().optional(),
    price_per_share: z.coerce.number().optional(),
    fees: z.coerce.number().optional(),
    total: z.coerce.number().optional(),
}).omit({ id: true }).partial().transform((data: any) => ({
    ...data,
    quantity: data.quantity?.toString(),
    price_per_share: data.price_per_share?.toString(),
    fees: data.fees?.toString(),
    total: data.total?.toString(),
}));

// Type exports
export type StocksManagerEvent = typeof stocksManagerEventsTable.$inferSelect;
export type NewStocksManagerEvent = typeof stocksManagerEventsTable.$inferInsert;
export type StocksManagerEventUpdate = Partial<Omit<NewStocksManagerEvent, 'id'>>;

// Legacy schema for backward compatibility (if needed)
export const SCHEMA_TABLE_STOCKS_MANAGER_EVENTS = insertStocksManagerEventSchema;
