import type { Generated, Insertable, Selectable, Updateable } from 'kysely';
import { z } from 'zod/v4';

export const TABLE_NAME_STOCKS_MANAGER_EVENTS = 'stocks_manager_events';

export const StocksManagerEventType = {
    DIVIDEND: 'Dividend',
    PURCHASE: 'Purchase',
    REVERSE_SPLIT: 'Reverse Split',
    SALE: 'Sale',
} as const;

export const SCHEMA_TABLE_STOCKS_MANAGER_EVENTS = z.object({
    id: z.number().transform(i => i as unknown as Generated<number>),
    date: z.iso.date(),
    symbol: z.string().max(10).uppercase(),
    type: z.enum(StocksManagerEventType),
    quantity: z.number().optional().default(0),
    price_per_share: z.number().optional().default(0),
    fees: z.number().optional().default(0),
    total: z.number().optional().default(0),
});

export type TableStocksManagerEvents = z.infer<typeof SCHEMA_TABLE_STOCKS_MANAGER_EVENTS>;
export type StocksManagerEvent = Selectable<TableStocksManagerEvents>;
export type NewStocksManagerEvent = Insertable<TableStocksManagerEvents>;
export type StocksManagerEventUpdate = Updateable<TableStocksManagerEvents>;

export type Database = {
    stocksManagerEvents: TableStocksManagerEvents;
}
