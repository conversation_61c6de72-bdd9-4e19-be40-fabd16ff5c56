import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Database configuration
const connectionString = process.env.DATABASE_URL || 'postgresql://localhost:5432/finance_manager';

let dbInstance: ReturnType<typeof drizzle> | null = null;
let clientInstance: ReturnType<typeof postgres> | null = null;

/**
 * Get the database instance (singleton pattern)
 */
export function getDatabase() {
    if (!dbInstance) {
        clientInstance = postgres(connectionString);
        dbInstance = drizzle(clientInstance, { schema });
    }
    return dbInstance;
}

/**
 * Get the postgres client instance (singleton pattern)
 */
export function getClient() {
    if (!clientInstance) {
        getDatabase(); // This will initialize both instances
    }
    return clientInstance!;
}
