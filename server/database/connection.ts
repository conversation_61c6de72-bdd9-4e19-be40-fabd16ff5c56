import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Database configuration
const connectionString = process.env.DATABASE_URL || 'postgresql://localhost:5432/finance_manager';

// Create the postgres client
const client = postgres(connectionString);

// Create the drizzle database instance
export const db = drizzle(client, { schema });

// Export the client for direct access if needed
export { client };
