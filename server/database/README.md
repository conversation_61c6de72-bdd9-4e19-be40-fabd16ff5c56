# Database Setup with Dr<PERSON>zle ORM

This project uses Drizzle ORM with PostgreSQL and includes a repository pattern for easy database abstraction.

## Architecture

### Repository Pattern
The database layer is organized using the repository pattern to provide:
- **Abstraction**: Easy switching between different database implementations
- **Testability**: Mock repositories for unit testing
- **Maintainability**: Clear separation of concerns

### Structure
```
server/database/
├── schema.ts                    # Drizzle schema definitions and Zod validation
├── connection.ts                # Database connection setup
├── migrations/                  # Auto-generated migration files
└── repositories/
    ├── interfaces.ts            # Repository interface definitions
    ├── drizzle-implementation.ts # Drizzle ORM implementation
    └── index.ts                 # Repository factory and exports
```

## Setup

### 1. Environment Configuration
Copy `.env.example` to `.env` and configure your PostgreSQL connection:
```bash
cp .env.example .env
```

Edit `.env`:
```env
DATABASE_URL=*********************************************/database_name
```

### 2. Install Dependencies
```bash
pnpm install
```

### 3. Generate and Run Migrations
```bash
# Generate migration files from schema
pnpm db:generate

# Apply migrations to database
pnpm db:migrate
```

### 4. Optional: Open Drizzle Studio
```bash
pnpm db:studio
```

## Usage

### Using the Repository
```typescript
import { repository } from '~/server/database/repositories';

// Create a new event
const newEvent = await repository.stocksManagerEvents.create({
    date: '2024-01-15',
    symbol: 'AAPL',
    type: 'Purchase',
    quantity: 100,
    price_per_share: 150.00,
    fees: 9.99,
    total: 15009.99,
});

// Find events by symbol
const appleEvents = await repository.stocksManagerEvents.findBySymbol('AAPL');

// Get aggregated data
const aggregated = await repository.stocksManagerEvents.getAggregatedDataBySymbol('AAPL');
```

### Using the Service Layer
```typescript
import { stocksManagerService } from '~/server/services/stocks-manager-service';

// Create with validation
const newEvent = await stocksManagerService.createEvent({
    date: '2024-01-15',
    symbol: 'aapl', // Will be automatically converted to uppercase
    type: 'Purchase',
    quantity: 100,
    price_per_share: 150.00,
    fees: 9.99,
    total: 15009.99,
});

// Get portfolio summary
const summary = await stocksManagerService.getPortfolioSummary();
```

## Schema

### Stocks Manager Events Table
- `id`: Serial primary key
- `date`: Date of the event
- `symbol`: Stock symbol (max 10 characters, uppercase)
- `type`: Event type (Dividend, Purchase, Reverse Split, Sale)
- `quantity`: Number of shares (decimal)
- `price_per_share`: Price per share (decimal)
- `fees`: Transaction fees (decimal)
- `total`: Total transaction amount (decimal)

## Validation

The project uses Zod for runtime validation:
- Input validation in service layer
- Schema validation using `drizzle-zod` integration
- Type-safe operations throughout the application

## Switching Database Implementations

To switch to a different database (e.g., MySQL, SQLite):

1. Create a new implementation in `repositories/`
2. Update the factory function in `repositories/index.ts`
3. Update the connection configuration
4. The rest of the application remains unchanged

Example:
```typescript
// repositories/mysql-implementation.ts
export class MySQLDatabaseRepository implements IDatabaseRepository {
    // Implementation for MySQL
}

// repositories/index.ts
export function createDatabaseRepository(): IDatabaseRepository {
    if (process.env.DB_TYPE === 'mysql') {
        return new MySQLDatabaseRepository(mysqlConnection);
    }
    return new DrizzleDatabaseRepository(postgresConnection);
}
```

## Available Scripts

- `pnpm db:generate` - Generate migration files from schema changes
- `pnpm db:migrate` - Apply pending migrations to database
- `pnpm db:studio` - Open Drizzle Studio for database management
