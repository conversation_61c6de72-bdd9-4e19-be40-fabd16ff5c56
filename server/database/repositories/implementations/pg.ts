import { eq, and, gte, lte, count } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import type { IStocksManagerEventRepository } from '~/server/database/repositories/interfaces';
import type { 
    StocksManagerEvent, 
    NewStocksManagerEvent, 
    StocksManagerEventUpdate 
} from '~/server/database/schema';
import { stocksManagerEventsTable } from '~/server/database/schema';

/**
 * PostgreSQL implementation of the stocks manager event repository using Drizzle ORM
 */
export class PostgreSQLStocksManagerEventRepository implements IStocksManagerEventRepository {
    constructor(private db: PostgresJsDatabase<any>) {}

    async create(event: NewStocksManagerEvent): Promise<StocksManagerEvent> {
        const [created] = await this.db
            .insert(stocksManagerEventsTable)
            .values(event)
            .returning();
        return created;
    }

    async findById(id: number): Promise<StocksManagerEvent | null> {
        const [event] = await this.db
            .select()
            .from(stocksManagerEventsTable)
            .where(eq(stocksManagerEventsTable.id, id));
        return event || null;
    }

    async findAll(filters?: {
        symbol?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
        limit?: number;
        offset?: number;
    }): Promise<StocksManagerEvent[]> {
        // Build where conditions
        const conditions = [];
        if (filters?.symbol) {
            conditions.push(eq(stocksManagerEventsTable.symbol, filters.symbol));
        }
        if (filters?.type) {
            conditions.push(eq(stocksManagerEventsTable.type, filters.type as any));
        }
        if (filters?.dateFrom) {
            conditions.push(gte(stocksManagerEventsTable.date, filters.dateFrom));
        }
        if (filters?.dateTo) {
            conditions.push(lte(stocksManagerEventsTable.date, filters.dateTo));
        }

        // Build the query step by step
        let queryBuilder = this.db.select().from(stocksManagerEventsTable);
        
        if (conditions.length > 0) {
            queryBuilder = queryBuilder.where(and(...conditions)) as any;
        }

        if (filters?.limit) {
            queryBuilder = queryBuilder.limit(filters.limit) as any;
        }
        if (filters?.offset) {
            queryBuilder = queryBuilder.offset(filters.offset) as any;
        }

        return await queryBuilder;
    }

    async update(id: number, updates: StocksManagerEventUpdate): Promise<StocksManagerEvent | null> {
        const [updated] = await this.db
            .update(stocksManagerEventsTable)
            .set(updates)
            .where(eq(stocksManagerEventsTable.id, id))
            .returning();
        return updated || null;
    }

    async delete(id: number): Promise<boolean> {
        const result = await this.db
            .delete(stocksManagerEventsTable)
            .where(eq(stocksManagerEventsTable.id, id));
        return result.length > 0;
    }

    async count(filters?: {
        symbol?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
    }): Promise<number> {
        // Build where conditions
        const conditions = [];
        if (filters?.symbol) {
            conditions.push(eq(stocksManagerEventsTable.symbol, filters.symbol));
        }
        if (filters?.type) {
            conditions.push(eq(stocksManagerEventsTable.type, filters.type as any));
        }
        if (filters?.dateFrom) {
            conditions.push(gte(stocksManagerEventsTable.date, filters.dateFrom));
        }
        if (filters?.dateTo) {
            conditions.push(lte(stocksManagerEventsTable.date, filters.dateTo));
        }

        let queryBuilder = this.db.select({ count: count() }).from(stocksManagerEventsTable);
        
        if (conditions.length > 0) {
            queryBuilder = queryBuilder.where(and(...conditions)) as any;
        }

        const [result] = await queryBuilder;
        return result.count;
    }
}
