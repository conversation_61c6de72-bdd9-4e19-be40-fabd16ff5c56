import type { StocksManagerEvent, NewStocksManagerEvent, StocksManagerEventUpdate } from '../schema';

/**
 * Interface for stocks manager event repository operations
 * This abstraction allows for easy switching between different database implementations
 */
export interface IStocksManagerEventRepository {
    /**
     * Create a new stocks manager event
     */
    create(event: NewStocksManagerEvent): Promise<StocksManagerEvent>;

    /**
     * Find a stocks manager event by ID
     */
    findById(id: number): Promise<StocksManagerEvent | null>;

    /**
     * Find all stocks manager events with optional filtering
     */
    findAll(filters?: {
        symbol?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
        limit?: number;
        offset?: number;
    }): Promise<StocksManagerEvent[]>;

    /**
     * Find stocks manager events by symbol
     */
    findBySymbol(symbol: string): Promise<StocksManagerEvent[]>;

    /**
     * Find stocks manager events by type
     */
    findByType(type: string): Promise<StocksManagerEvent[]>;

    /**
     * Find stocks manager events within a date range
     */
    findByDateRange(dateFrom: string, dateTo: string): Promise<StocksManagerEvent[]>;

    /**
     * Update a stocks manager event by ID
     */
    update(id: number, updates: StocksManagerEventUpdate): Promise<StocksManagerEvent | null>;

    /**
     * Delete a stocks manager event by ID
     */
    delete(id: number): Promise<boolean>;

    /**
     * Get total count of stocks manager events with optional filtering
     */
    count(filters?: {
        symbol?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
    }): Promise<number>;

    /**
     * Get aggregated data for a symbol (total quantity, total value, etc.)
     */
    getAggregatedDataBySymbol(symbol: string): Promise<{
        totalQuantity: number;
        totalValue: number;
        totalFees: number;
        eventCount: number;
    }>;
}

/**
 * Main database repository interface that combines all repository interfaces
 * This allows for easy extension when adding new entities
 */
export interface IDatabaseRepository {
    stocksManagerEvents: IStocksManagerEventRepository;
}
