import type { StocksManagerEvent, NewStocksManagerEvent, StocksManagerEventUpdate } from '~/server/database/schema';

/**
 * Interface for stocks manager event repository operations
 * This abstraction allows for easy switching between different database implementations
 */
export interface IStocksManagerEventRepository {
    /**
     * Create a new stocks manager event
     */
    create(event: NewStocksManagerEvent): Promise<StocksManagerEvent>;

    /**
     * Find a stocks manager event by ID
     */
    findById(id: number): Promise<StocksManagerEvent | null>;

    /**
     * Find all stocks manager events with optional filtering
     */
    findAll(filters?: {
        symbol?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
        limit?: number;
        offset?: number;
    }): Promise<StocksManagerEvent[]>;

    /**
     * Update a stocks manager event by ID
     */
    update(id: number, updates: StocksManagerEventUpdate): Promise<StocksManagerEvent | null>;

    /**
     * Delete a stocks manager event by ID
     */
    delete(id: number): Promise<boolean>;

    /**
     * Get total count of stocks manager events with optional filtering
     */
    count(filters?: {
        symbol?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
    }): Promise<number>;
}

/**
 * Main database repository interface that combines all repository interfaces
 * This allows for easy extension when adding new entities
 */
export interface IDatabaseRepository {
    stocksManagerEvents: IStocksManagerEventRepository;
}
