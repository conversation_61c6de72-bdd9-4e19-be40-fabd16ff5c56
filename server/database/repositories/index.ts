import { db } from '../connection';
import { DrizzleDatabaseRepository } from './drizzle-implementation';
import type { IDatabaseRepository } from './interfaces';

/**
 * Create and return the database repository instance
 * This factory function allows for easy switching between different implementations
 */
export function createDatabaseRepository(): IDatabaseRepository {
    return new DrizzleDatabaseRepository(db);
}

/**
 * Default repository instance
 * Use this for most operations unless you need a custom configuration
 */
export const repository = createDatabaseRepository();

// Re-export types and interfaces for convenience
export type { 
    IDatabaseRepository, 
    IStocksManagerEventRepository 
} from './interfaces';
export type { 
    StocksManagerEvent, 
    NewStocksManagerEvent, 
    StocksManagerEventUpdate 
} from '../schema';
