import { getDatabase } from '~/server/database/connection';
import { PostgreSQLStocksManagerEventRepository } from './implementations/pg';
import type { IDatabaseRepository, IStocksManagerEventRepository } from './interfaces';

/**
 * PostgreSQL database repository implementation
 */
class PostgreSQLDatabaseRepository implements IDatabaseRepository {
    public readonly stocksManagerEvents: IStocksManagerEventRepository;

    constructor() {
        const db = getDatabase();
        this.stocksManagerEvents = new PostgreSQLStocksManagerEventRepository(db);
    }
}

/**
 * Create and return the database repository instance
 * This factory function allows for easy switching between different implementations
 */
export function createDatabaseRepository(): IDatabaseRepository {
    return new PostgreSQLDatabaseRepository();
}

/**
 * Default repository instance
 * Use this for most operations unless you need a custom configuration
 */
export const repository = createDatabaseRepository();

// Re-export types and interfaces for convenience
export type {
    IDatabaseRepository,
    IStocksManagerEventRepository
} from './interfaces';
export type {
    StocksManagerEvent,
    NewStocksManagerEvent,
    StocksManagerEventUpdate
} from '~/server/database/schema';
