import { repository } from '~/server/database/repositories';
import {
    insertStocksManagerEventSchema,
    updateStocksManagerEventSchema
} from '~/server/database/schema';
import type {
    StocksManagerEvent
} from '~/server/database/repositories';

/**
 * Service layer for stocks manager operations
 * This layer handles business logic and validation
 */
export class StocksManagerService {
    /**
     * Create a new stocks manager event with validation
     */
    async createEvent(eventData: unknown): Promise<StocksManagerEvent> {
        // Validate the input data using Zod schema
        const validatedData = insertStocksManagerEventSchema.parse(eventData);
        
        // Create the event using the repository
        return await repository.stocksManagerEvents.create(validatedData);
    }

    /**
     * Get a stocks manager event by ID
     */
    async getEventById(id: number): Promise<StocksManagerEvent | null> {
        return await repository.stocksManagerEvents.findById(id);
    }

    /**
     * Get all stocks manager events with optional filtering
     */
    async getAllEvents(filters?: {
        symbol?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
        limit?: number;
        offset?: number;
    }): Promise<StocksManagerEvent[]> {
        return await repository.stocksManagerEvents.findAll(filters);
    }

    /**
     * Get stocks manager events by symbol
     */
    async getEventsBySymbol(symbol: string): Promise<StocksManagerEvent[]> {
        return await repository.stocksManagerEvents.findAll({ symbol: symbol.toUpperCase() });
    }

    /**
     * Get stocks manager events by type
     */
    async getEventsByType(type: string): Promise<StocksManagerEvent[]> {
        return await repository.stocksManagerEvents.findAll({ type });
    }

    /**
     * Get stocks manager events within a date range
     */
    async getEventsByDateRange(dateFrom: string, dateTo: string): Promise<StocksManagerEvent[]> {
        return await repository.stocksManagerEvents.findAll({ dateFrom, dateTo });
    }

    /**
     * Update a stocks manager event with validation
     */
    async updateEvent(id: number, updateData: unknown): Promise<StocksManagerEvent | null> {
        // Validate the update data using Zod schema
        const validatedData = updateStocksManagerEventSchema.parse(updateData);
        
        // Update the event using the repository
        return await repository.stocksManagerEvents.update(id, validatedData);
    }

    /**
     * Delete a stocks manager event
     */
    async deleteEvent(id: number): Promise<boolean> {
        return await repository.stocksManagerEvents.delete(id);
    }

    /**
     * Get count of stocks manager events with optional filtering
     */
    async getEventCount(filters?: {
        symbol?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
    }): Promise<number> {
        return await repository.stocksManagerEvents.count(filters);
    }

    /**
     * Get aggregated data for a symbol
     */
    async getAggregatedDataBySymbol(symbol: string): Promise<{
        totalQuantity: number;
        totalValue: number;
        totalFees: number;
        eventCount: number;
    }> {
        const events = await repository.stocksManagerEvents.findAll({ symbol: symbol.toUpperCase() });

        const totalQuantity = events.reduce((sum, event) => sum + Number(event.quantity || 0), 0);
        const totalValue = events.reduce((sum, event) => sum + Number(event.total || 0), 0);
        const totalFees = events.reduce((sum, event) => sum + Number(event.fees || 0), 0);
        const eventCount = events.length;

        return {
            totalQuantity,
            totalValue,
            totalFees,
            eventCount,
        };
    }

    /**
     * Get portfolio summary (aggregated data for all symbols)
     */
    async getPortfolioSummary(): Promise<{
        symbols: string[];
        totalEvents: number;
        totalValue: number;
        totalFees: number;
    }> {
        // This is a more complex operation that might require multiple queries
        // For now, we'll implement a basic version
        const allEvents = await repository.stocksManagerEvents.findAll();
        
        const symbols = [...new Set(allEvents.map(event => event.symbol))];
        const totalEvents = allEvents.length;
        const totalValue = allEvents.reduce((sum, event) => sum + Number(event.total || 0), 0);
        const totalFees = allEvents.reduce((sum, event) => sum + Number(event.fees || 0), 0);

        return {
            symbols,
            totalEvents,
            totalValue,
            totalFees,
        };
    }
}

// Export a default instance
export const stocksManagerService = new StocksManagerService();
