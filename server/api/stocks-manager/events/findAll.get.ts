import { defineE<PERSON><PERSON><PERSON><PERSON>, getQuery, createError } from 'h3';
import { stocksManagerService } from '~/server/services/stocks-manager-service';

export default defineEventHandler(async (event) => {
    try {
        const query = getQuery(event);
        
        // Extract query parameters
        const filters = {
            symbol: query.symbol as string | undefined,
            type: query.type as string | undefined,
            dateFrom: query.dateFrom as string | undefined,
            dateTo: query.dateTo as string | undefined,
            limit: query.limit ? parseInt(query.limit as string) : undefined,
            offset: query.offset ? parseInt(query.offset as string) : undefined,
        };

        // Remove undefined values
        const cleanFilters = Object.fromEntries(
            Object.entries(filters).filter(([_, value]) => value !== undefined)
        );

        const events = await stocksManagerService.getAllEvents(cleanFilters);
        
        return {
            success: true,
            data: events,
            count: events.length,
        };
    } catch (error) {
        console.error('Error fetching stocks manager events:', error);
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to fetch stocks manager events',
        });
    }
});
