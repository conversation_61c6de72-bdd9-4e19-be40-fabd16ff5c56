import { stocksManagerService } from '../services/stocks-manager-service';

export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        
        const newEvent = await stocksManagerService.createEvent(body);
        
        return {
            success: true,
            data: newEvent,
        };
    } catch (error) {
        console.error('Error creating stocks manager event:', error);
        
        // Handle validation errors
        if (error instanceof Error && error.name === 'ZodError') {
            throw createError({
                statusCode: 400,
                statusMessage: 'Invalid input data',
                data: error.message,
            });
        }
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to create stocks manager event',
        });
    }
});
